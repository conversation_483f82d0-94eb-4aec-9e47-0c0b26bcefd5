{"name": "temp-email-forwarder-frontend", "version": "1.0.0", "description": "临时邮箱转发系统 - 前端界面", "main": "index.html", "scripts": {"start": "node ../server.js", "dev": "node ../server.js", "serve": "python -m http.server 8000", "build": "echo 'No build process needed for static frontend'", "deploy": "echo 'Ready for deployment'"}, "keywords": ["temp-email", "email-forwarding", "frontend", "javascript", "html", "css"], "author": "Your Name", "license": "MIT", "devDependencies": {}, "dependencies": {}, "engines": {"node": ">=12.0.0"}, "repository": {"type": "git", "url": "https://github.com/yourusername/temp-email-forwarder.git", "directory": "frontend"}}