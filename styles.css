* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: 20px;
}

.main-content {
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    overflow: hidden;
}

.header {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    padding: 30px;
    text-align: center;
}

.header h1 {
    font-size: 2.5em;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
}

.header p {
    opacity: 0.9;
    font-size: 1.1em;
}

.content {
    padding: 30px;
}

.section {
    margin-bottom: 30px;
    padding: 25px;
    border: 2px solid #f0f0f0;
    border-radius: 10px;
    transition: all 0.3s ease;
}

.section:hover {
    border-color: #4facfe;
    box-shadow: 0 5px 15px rgba(79, 172, 254, 0.1);
}

.section h2 {
    color: #4facfe;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.5em;
}

.config-status {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #e9ecef;
}

.status-item:last-child {
    border-bottom: none;
}

.status-label {
    font-weight: 600;
    color: #495057;
    min-width: 120px;
}

.status-value {
    flex: 1;
    margin: 0 15px;
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
}

.status-indicator {
    font-size: 1.2em;
    min-width: 25px;
    text-align: center;
}

.btn {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    border: none;
    padding: 12px 25px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 600;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(79, 172, 254, 0.3);
}

.btn-danger {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
}

.btn-success {
    background: linear-gradient(135deg, #51cf66 0%, #40c057 100%);
}

.email-list {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-top: 20px;
}

.email-item {
    background: white;
    padding: 15px;
    margin-bottom: 10px;
    border-radius: 8px;
    border-left: 4px solid #4facfe;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.email-item:last-child {
    margin-bottom: 0;
}

.email-info {
    flex: 1;
}

.email-address {
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
}

.email-time {
    font-size: 0.9em;
    color: #666;
}

.monitor-status {
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    margin-top: 20px;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
}

.status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #dc3545;
}

.status-dot.active {
    background: #28a745;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.email-notification {
    background: #e8f5e8;
    border: 2px solid #28a745;
    border-radius: 10px;
    padding: 20px;
    margin-top: 20px;
    animation: slideIn 0.5s ease;
}

@keyframes slideIn {
    from { transform: translateY(-20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

.notification-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
    color: #28a745;
    font-weight: 600;
}

.notification-content {
    background: white;
    padding: 15px;
    border-radius: 8px;
    margin-top: 10px;
}

.verification-code {
    background: #fff3cd;
    border: 2px solid #ffc107;
    border-radius: 8px;
    padding: 15px;
    margin: 15px 0;
    text-align: center;
}

.verification-code .code {
    font-size: 2em;
    font-weight: bold;
    color: #856404;
    font-family: 'Courier New', monospace;
}

/* 侧边栏样式 */
.sidebar {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.ad-space {
    background: white;
    border-radius: 15px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    min-height: 250px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    border: 2px dashed #ddd;
}

.ad-space h3 {
    color: #666;
    margin-bottom: 10px;
}

.ad-space p {
    color: #999;
    font-size: 0.9em;
}

.stats-card {
    background: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.stats-card h3 {
    color: #4facfe;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #f0f0f0;
}

.stat-item:last-child {
    border-bottom: none;
}

.stat-value {
    font-weight: bold;
    color: #4facfe;
}

.env-help-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.env-help-content {
    background: white;
    border-radius: 15px;
    padding: 30px;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
    position: relative;
}

.env-help-close {
    position: absolute;
    top: 15px;
    right: 20px;
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
}

.env-var {
    background: #f8f9fa;
    border-left: 4px solid #4facfe;
    padding: 15px;
    margin: 10px 0;
    border-radius: 0 8px 8px 0;
}

.env-var-name {
    font-family: 'Courier New', monospace;
    font-weight: bold;
    color: #4facfe;
    margin-bottom: 5px;
}

.env-var-desc {
    color: #666;
    font-size: 0.9em;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        grid-template-columns: 1fr;
        padding: 10px;
    }
    
    .header h1 {
        font-size: 2em;
    }
    
    .content {
        padding: 20px;
    }
}

.hidden {
    display: none;
}

.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #4facfe;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes slideInRight {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes slideOutRight {
    from { transform: translateX(0); opacity: 1; }
    to { transform: translateX(100%); opacity: 0; }
}