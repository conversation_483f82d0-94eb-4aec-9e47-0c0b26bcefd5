<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>临时邮箱转发系统</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <div class="main-content">
            <div class="header">
                <h1>
                    <i class="fas fa-envelope"></i>
                    临时邮箱转发系统
                </h1>
                <p>基于Cloudflare的智能邮件转发服务</p>
            </div>

            <div class="content">
                <!-- 系统状态区域 -->
                <div class="section">
                    <h2><i class="fas fa-cog"></i> 系统状态</h2>
                    <div class="config-status" id="configStatus">
                        <div class="status-item">
                            <span class="status-label">QQ邮箱:</span>
                            <span class="status-value" id="qqEmailStatus">检查中...</span>
                            <span class="status-indicator" id="qqEmailIndicator">⏳</span>
                        </div>
                        <div class="status-item">
                            <span class="status-label">Cloudflare API:</span>
                            <span class="status-value" id="cfApiStatus">检查中...</span>
                            <span class="status-indicator" id="cfApiIndicator">⏳</span>
                        </div>
                        <div class="status-item">
                            <span class="status-label">域名:</span>
                            <span class="status-value" id="domainStatus">检查中...</span>
                            <span class="status-indicator" id="domainIndicator">⏳</span>
                        </div>
                        <div class="status-item">
                            <span class="status-label">系统状态:</span>
                            <span class="status-value" id="systemStatus">初始化中...</span>
                            <span class="status-indicator" id="systemIndicator">⏳</span>
                        </div>
                    </div>
                    <button class="btn" onclick="refreshConfig()">
                        <i class="fas fa-refresh"></i> 刷新配置
                    </button>
                    <button class="btn" onclick="showEnvHelp()" style="margin-left: 10px;">
                        <i class="fas fa-question-circle"></i> 环境变量说明
                    </button>
                </div>

                <!-- 临时邮箱管理 -->
                <div class="section">
                    <h2><i class="fas fa-plus-circle"></i> 临时邮箱管理</h2>
                    <button class="btn" onclick="createTempEmail()">
                        <i class="fas fa-plus"></i> 创建临时邮箱
                    </button>
                    <button class="btn btn-danger" onclick="deleteSelectedEmails()">
                        <i class="fas fa-trash"></i> 删除选中邮箱
                    </button>
                    
                    <div class="email-list" id="emailList">
                        <p style="text-align: center; color: #666;">暂无临时邮箱</p>
                    </div>
                </div>

                <!-- 邮件监控 -->
                <div class="section">
                    <h2><i class="fas fa-eye"></i> 邮件监控</h2>
                    <div class="monitor-status">
                        <div class="status-indicator">
                            <div class="status-dot" id="statusDot"></div>
                            <span id="statusText">监控已停止</span>
                        </div>
                        <button class="btn btn-success" id="monitorBtn" onclick="toggleMonitor()">
                            <i class="fas fa-play"></i> 开始监控
                        </button>
                        <button class="btn" onclick="checkOnce()">
                            <i class="fas fa-search"></i> 检查一次
                        </button>
                    </div>
                    
                    <div id="monitorLog" class="hidden">
                        <h3 style="margin-top: 20px; color: #4facfe;">监控日志</h3>
                        <div id="logContent" style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-top: 10px; max-height: 300px; overflow-y: auto; font-family: monospace; font-size: 0.9em;"></div>
                    </div>
                </div>

                <!-- 邮件通知区域 -->
                <div id="emailNotifications"></div>
            </div>
        </div>

        <!-- 侧边栏 -->
        <div class="sidebar">
            <!-- 广告位1 -->
            <div class="ad-space">
                <h3><i class="fas fa-ad"></i> 广告位</h3>
                <p>这里可以放置广告内容</p>
                <p>尺寸: 300x250</p>
            </div>

            <!-- 统计信息 -->
            <div class="stats-card">
                <h3><i class="fas fa-chart-bar"></i> 统计信息</h3>
                <div class="stat-item">
                    <span>临时邮箱数量</span>
                    <span class="stat-value" id="emailCount">0</span>
                </div>
                <div class="stat-item">
                    <span>收到邮件数量</span>
                    <span class="stat-value" id="receivedCount">0</span>
                </div>
                <div class="stat-item">
                    <span>监控状态</span>
                    <span class="stat-value" id="monitorStatus">已停止</span>
                </div>
                <div class="stat-item">
                    <span>运行时间</span>
                    <span class="stat-value" id="runTime">00:00:00</span>
                </div>
            </div>

            <!-- 广告位2 -->
            <div class="ad-space">
                <h3><i class="fas fa-bullhorn"></i> 推广位</h3>
                <p>这里可以放置推广内容</p>
                <p>尺寸: 300x200</p>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>